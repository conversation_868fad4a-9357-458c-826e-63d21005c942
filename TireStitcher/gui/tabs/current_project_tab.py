"""
Current project tab for the Tire Panorama Tool.

Displays and manages the currently selected tire project.
"""
import os
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
from functools import partial

def create_current_project_tab(self):
    """
    Create the current project tab.

    Args:
        self: TirePanoramaApp instance
    """
    # Create a container frame for the scrollable content
    self.current_project_container = ttk.Frame(self.current_project_tab)
    self.current_project_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # Create canvas with scrollbar for scrolling
    self.current_project_canvas = tk.Canvas(self.current_project_container, highlightthickness=0)
    self.current_project_scrollbar = ttk.Scrollbar(self.current_project_container, orient=tk.VERTICAL,
                                                  command=self.current_project_canvas.yview)

    # Configure canvas
    self.current_project_canvas.configure(yscrollcommand=self.current_project_scrollbar.set)

    # Pack canvas and scrollbar
    self.current_project_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    self.current_project_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

    # Create a frame inside the canvas to hold the content
    self.current_project_content = ttk.Frame(self.current_project_canvas)

    # Create a window in the canvas to display the frame
    self.canvas_frame_id = self.current_project_canvas.create_window(
        (0, 0), window=self.current_project_content, anchor=tk.NW, tags="content")

    # Configure canvas scrolling region when frame changes size
    self.current_project_content.bind("<Configure>", lambda event: configure_current_project_scroll_region(self, event))

    # We'll bind mouse wheel events when the tab is selected
    # This is done in the on_tab_selected function to avoid conflicts

    # Configure canvas to expand with window
    self.current_project_canvas.bind("<Configure>", lambda event: on_canvas_configure(self, event))

    # Create empty state message
    self.no_project_label = ttk.Label(
        self.current_project_content,
        text="No ETO / SID selected. Select an ETO / SID from the ETO / SID tab.",
        font=("Segoe UI", 12),
        foreground="#888888"
    )
    self.no_project_label.pack(expand=True)

    # Create project content (initially hidden)
    self.project_content_frame = ttk.Frame(self.current_project_content)

    # Project header
    _create_project_header(self, self.project_content_frame)

    # Create subproject panels
    _create_subproject_panels(self, self.project_content_frame)

    # Create a variable to store the currently selected subproject
    self.selected_subproject = None

    # Create styles for selected subproject panels and serial number
    style = ttk.Style()
    style.configure("Selected.TLabelframe",
                   background="#e0f0ff",
                   foreground="#555555")
    style.configure("Success.TButton",
                   background="#32CD32",
                   foreground="white")
    style.configure("SerialNumber.TLabel",
                   font=("Segoe UI", 20, "bold"),
                   foreground="#0066cc")

    # Initial update
    self.update_current_project_display = partial(update_current_project_display, self)

    # Hook select_subproject method for use in panels
    if not hasattr(self, 'select_subproject'):
        self.select_subproject = lambda subproject_type: select_subproject(self, subproject_type)

    # Force an update when the tab is selected
    def on_tab_changed(event):
        # Get selected tab
        tab_id = event.widget.select()
        tab_name = event.widget.tab(tab_id, "text")

        # If selecting Current Tire tab, update display
        if tab_name == "Current Tire" and hasattr(self, 'update_current_project_display'):
            self.update_current_project_display()

    self.notebook.bind("<<NotebookTabChanged>>", on_tab_changed)

    # Initial update
    self.update_current_project_display()

def _create_project_header(self, parent):
    """Create the project header section"""
    header_frame = ttk.Frame(parent)
    header_frame.pack(fill=tk.X, padx=10, pady=10)

    # Serial Number - large and prominent at the top
    serial_frame = ttk.LabelFrame(header_frame, text="Tire Identification")
    serial_frame.pack(fill=tk.X, pady=(0, 15))

    title_frame = ttk.Frame(serial_frame)
    title_frame.pack(fill=tk.X, padx=10, pady=10)

    # Serial Number Label
    ttk.Label(
        title_frame,
        text="Serial Number:",
        font=("Segoe UI", 12)
    ).pack(side=tk.LEFT)

    # Serial Number Value - large, bold font
    self.project_serial_var = tk.StringVar(value="Serial Number")
    # Use a direct text value instead of textvariable
    self.project_serial_label = tk.Label(
        title_frame,
        text="Serial Number",  # Initial text
        font=("Segoe UI", 20, "bold"),
        foreground="#0066cc"
    )
    self.project_serial_label.pack(side=tk.LEFT, padx=(10, 0))

    # No need for separator as the LabelFrame provides visual separation



    # Project metadata
    info_frame = ttk.Frame(header_frame)
    info_frame.pack(fill=tk.X, pady=5)

    # Plan info
    plan_frame = ttk.Frame(info_frame)
    plan_frame.pack(side=tk.LEFT, padx=(0, 20))

    ttk.Label(plan_frame, text="Plan:", font=("Segoe UI", 9, "bold")).pack(side=tk.LEFT)
    self.project_plan_name_var = tk.StringVar(value="Plan Name")
    self.plan_name_label = tk.Label(plan_frame, text="Plan Name")
    self.plan_name_label.pack(side=tk.LEFT, padx=5)

    # Created date
    date_frame = ttk.Frame(info_frame)
    date_frame.pack(side=tk.LEFT, padx=(0, 20))

    ttk.Label(date_frame, text="Created:", font=("Segoe UI", 9, "bold")).pack(side=tk.LEFT)
    self.project_created_date_var = tk.StringVar(value="2023-01-01")
    self.created_date_label = tk.Label(date_frame, text="2023-01-01")
    self.created_date_label.pack(side=tk.LEFT, padx=5)

    # Dimensions section using simple horizontal frames instead of grid
    dimensions_frame = ttk.LabelFrame(parent, text="Tire Dimensions")
    dimensions_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

    # Horizontal layout for Width and Diameter
    top_row = ttk.Frame(dimensions_frame)
    top_row.pack(fill=tk.X, padx=10, pady=5)

    # Width
    width_label = ttk.Label(top_row, text="Width:", width=12, anchor=tk.W)
    width_label.pack(side=tk.LEFT)

    self.project_width_var = tk.StringVar(value="Not set")
    self.width_value_label = tk.Label(top_row, text="Not set", width=15, anchor=tk.W)
    self.width_value_label.pack(side=tk.LEFT)

    width_edit = ttk.Button(top_row, text="Edit", width=8,
                           command=lambda: edit_project_dimension(self, 'width'))
    width_edit.pack(side=tk.LEFT, padx=5)

    # Spacer
    ttk.Frame(top_row, width=20).pack(side=tk.LEFT)

    # Diameter
    diameter_label = ttk.Label(top_row, text="Diameter:", width=12, anchor=tk.W)
    diameter_label.pack(side=tk.LEFT)

    self.project_diameter_var = tk.StringVar(value="Not set")
    self.diameter_value_label = tk.Label(top_row, text="Not set", width=15, anchor=tk.W)
    self.diameter_value_label.pack(side=tk.LEFT)

    diameter_edit = ttk.Button(top_row, text="Edit", width=8,
                              command=lambda: edit_project_dimension(self, 'diameter'))
    diameter_edit.pack(side=tk.LEFT, padx=5)

    # Circumference row
    bottom_row = ttk.Frame(dimensions_frame)
    bottom_row.pack(fill=tk.X, padx=10, pady=5)

    circumference_label = ttk.Label(bottom_row, text="Circumference:", width=12, anchor=tk.W)
    circumference_label.pack(side=tk.LEFT)

    self.project_circumference_var = tk.StringVar(value="Not set")
    self.circumference_value_label = tk.Label(bottom_row, text="Not set", width=15, anchor=tk.W)
    self.circumference_value_label.pack(side=tk.LEFT)

    circumference_edit = ttk.Button(bottom_row, text="Edit", width=8,
                                   command=lambda: edit_project_dimension(self, 'circumference'))
    circumference_edit.pack(side=tk.LEFT, padx=5)

    # Separator
    ttk.Separator(parent, orient=tk.HORIZONTAL).pack(fill=tk.X, padx=10, pady=5)

def _create_subproject_panels(self, parent):
    """Create panels for each subproject"""
    panels_frame = ttk.Frame(parent)
    panels_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # Create a panel for each subproject
    self.subproject_panels = {}

    # Map subproject types to display names
    subproject_names = {
        "frontal": "Frontal Panorama",
        "left": "Left Side Panorama",
        "right": "Right Side Panorama"
    }

    # Import the panel class at function level to avoid dependency cycles
    from gui.components.subproject_panel import SubprojectPanel

    # Create panels
    for subproject_type, display_name in subproject_names.items():
        subproject_panel = SubprojectPanel(
            panels_frame,
            self,
            subproject_type,
            display_name
        )
        subproject_panel.pack(fill=tk.X, pady=10)

        self.subproject_panels[subproject_type] = subproject_panel

def update_current_project_display(self):
    """Update the display based on the current project"""
    if not hasattr(self, 'current_project') or not self.current_project:
        # Show empty state
        if hasattr(self, 'no_project_label'):
            self.no_project_label.pack(expand=True)

        if hasattr(self, 'project_content_frame'):
            self.project_content_frame.pack_forget()

        return

    # Hide empty state, show project content
    if hasattr(self, 'no_project_label'):
        self.no_project_label.pack_forget()

    if hasattr(self, 'project_content_frame'):
        self.project_content_frame.pack(fill=tk.BOTH, expand=True)

    # Update project header - force update with direct text setting
    if hasattr(self, 'project_serial_label'):
        serial_number = self.current_project.serial_number or "Unknown"
        self.project_serial_label.config(text=serial_number)

    # Find plan name
    plan_name = "Unknown Plan"
    for plan in self.plans:
        if plan.id == self.current_project.template_id:
            plan_name = plan.name
            break

    if hasattr(self, 'plan_name_label'):
        self.plan_name_label.config(text=plan_name)

    # Format created date
    created_date = "Unknown"
    try:
        date_obj = datetime.fromisoformat(self.current_project.created_date)
        created_date = date_obj.strftime("%Y-%m-%d %H:%M")
    except:
        if hasattr(self.current_project, 'created_date'):
            created_date = self.current_project.created_date

    if hasattr(self, 'created_date_label'):
        self.created_date_label.config(text=created_date)

    # Update dimension values
    from utils.dimension_calculator import format_dimension

    if hasattr(self, 'width_value_label'):
        formatted_width = format_dimension(self.current_project.width)
        self.width_value_label.config(text=formatted_width)

    if hasattr(self, 'diameter_value_label'):
        formatted_diameter = format_dimension(self.current_project.diameter)
        self.diameter_value_label.config(text=formatted_diameter)

    if hasattr(self, 'circumference_value_label'):
        formatted_circumference = format_dimension(self.current_project.circumference)
        self.circumference_value_label.config(text=formatted_circumference)

    # Reset selection if needed
    if self.selected_subproject:
        # Check if selected subproject still exists
        if self.selected_subproject not in self.subproject_panels:
            self.selected_subproject = None

    # Update each subproject panel - only update the selected one fully
    for subproject_type, panel in self.subproject_panels.items():
        # Set selection state
        is_selected = (subproject_type == self.selected_subproject)
        panel.set_selected(is_selected)

        # Only do a full update for the selected panel
        # For non-selected panels, just update the status text
        if is_selected:
            panel.update()
        else:
            # Minimal update for non-selected panels
            subproject = self.current_project.subprojects.get(subproject_type, {})
            status_text = panel._format_status(subproject.get("status", "not_started"))
            panel.status_var.set(status_text)

def select_subproject(self, subproject_type):
    """
    Set the selected subproject.

    Args:
        self: TirePanoramaApp instance
        subproject_type: Type of subproject ("frontal", "left", or "right") or None to deselect
    """
    # Only proceed if we have a current project
    if not self.current_project:
        return

    # If subproject_type is None, deselect the current selection
    if subproject_type is None:
        # If there was a previous selection, deselect it
        if self.selected_subproject and self.selected_subproject in self.subproject_panels:
            self.subproject_panels[self.selected_subproject].set_selected(False)

        # Clear selection
        self.selected_subproject = None
        self.status_var.set("No subproject selected")
        return

    # If already selected, do nothing (selection toggling is handled in subproject_panel)
    if self.selected_subproject == subproject_type:
        return

    # If there was a previous selection, deselect it
    if self.selected_subproject and self.selected_subproject in self.subproject_panels:
        self.subproject_panels[self.selected_subproject].set_selected(False)

    # Store new selection
    self.selected_subproject = subproject_type

    # Update UI - only update the newly selected panel
    # This avoids unnecessary updates to all panels
    if subproject_type in self.subproject_panels:
        # Set selected state for the new panel
        # The set_selected method will only update if the state changes
        self.subproject_panels[subproject_type].set_selected(True)

    # Update status
    self.status_var.set(f"Selected {subproject_type} subproject")

def edit_project_dimension(self, dimension_type):
    """
    Edit a project dimension value

    Args:
        dimension_type: Type of dimension ("width", "diameter", or "circumference")
    """
    if not self.current_project:
        return

    # Get current value
    current_value = ""
    if dimension_type == "width":
        current_value = str(self.current_project.width) if self.current_project.width else ""
    elif dimension_type == "diameter":
        current_value = str(self.current_project.diameter) if self.current_project.diameter else ""
    elif dimension_type == "circumference":
        current_value = str(self.current_project.circumference) if self.current_project.circumference else ""

    # Create a simple dialog for editing the value
    dialog = tk.Toplevel(self.root)
    dialog.title(f"Edit {dimension_type.title()}")
    dialog.geometry("300x120")
    dialog.transient(self.root)
    dialog.grab_set()

    # Make dialog modal
    dialog.focus_set()

    # Center dialog
    x = self.root.winfo_x() + (self.root.winfo_width() // 2) - (300 // 2)
    y = self.root.winfo_y() + (self.root.winfo_height() // 2) - (120 // 2)
    dialog.geometry(f"+{x}+{y}")

    # Create content
    content_frame = ttk.Frame(dialog, padding=10)
    content_frame.pack(fill=tk.BOTH, expand=True)

    # Label
    ttk.Label(
        content_frame,
        text=f"Enter {dimension_type} value (mm):"
    ).pack(pady=(0, 5))

    # Entry
    value_var = tk.StringVar(value=current_value)
    entry = ttk.Entry(content_frame, textvariable=value_var, width=20)
    entry.pack(pady=5)
    entry.focus_set()
    entry.select_range(0, tk.END)

    # Buttons
    buttons_frame = ttk.Frame(content_frame)
    buttons_frame.pack(fill=tk.X, pady=10)

    def on_cancel():
        dialog.destroy()

    def on_save():
        # Parse value
        try:
            value_str = value_var.get().strip()
            if not value_str:
                value = None
            else:
                value = float(value_str)

            # Update project
            if dimension_type == "width":
                self.project_manager.update_project(self.current_project.id, width=value)
            elif dimension_type == "diameter":
                self.project_manager.update_project(self.current_project.id, diameter=value)
            elif dimension_type == "circumference":
                self.project_manager.update_project(self.current_project.id, circumference=value)

            # Reload current project data
            self.current_project = self.project_manager.get_project(self.current_project.id)

            # Update display
            self.update_current_project_display()

            dialog.destroy()
        except ValueError:
            messagebox.showerror("Error", f"Invalid {dimension_type} value. Please enter a number.", parent=dialog)

    ttk.Button(buttons_frame, text="Cancel", command=on_cancel).pack(side=tk.RIGHT, padx=5)
    ttk.Button(buttons_frame, text="Save", command=on_save, style="Accent.TButton").pack(side=tk.RIGHT, padx=5)

    # Bind Enter key
    entry.bind("<Return>", lambda event: on_save())
    dialog.bind("<Escape>", lambda event: on_cancel())

# view_panorama function removed

def configure_current_project_scroll_region(self, event):
    """Configure the scroll region of the current project canvas"""
    if hasattr(self, 'current_project_canvas'):
        # Update the scrollregion to encompass the inner frame
        self.current_project_canvas.configure(scrollregion=self.current_project_canvas.bbox("all"))

def on_mousewheel(self, event, canvas):
    """Handle mousewheel scrolling on Windows"""
    canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

def on_linux_mousewheel(self, event, canvas, direction):
    """Handle mousewheel scrolling on Linux"""
    canvas.yview_scroll(direction, "units")

def on_canvas_configure(self, event):
    """Handle canvas resize"""
    if hasattr(self, 'current_project_canvas') and hasattr(self, 'current_project_content'):
        # Update the width of the canvas window to fit the canvas width
        canvas_width = event.width
        self.current_project_canvas.itemconfig(self.canvas_frame_id, width=canvas_width)



def on_tab_selected(self, event):
    """Handle tab selection"""
    # Get selected tab
    tab_id = event.widget.select()
    tab_name = event.widget.tab(tab_id, "text")

    # Unbind mouse wheel events from all tabs first
    if hasattr(self, 'root'):
        self.root.unbind_all("<MouseWheel>")
        self.root.unbind_all("<Button-4>")
        self.root.unbind_all("<Button-5>")

    # If selecting Current Project tab, update display and bind scrolling
    if tab_name == "Current Project":
        if hasattr(self, 'update_current_project_display'):
            self.update_current_project_display()

        # Reset scroll position to top when tab is selected
        if hasattr(self, 'current_project_canvas'):
            self.current_project_canvas.yview_moveto(0)

            # Bind mouse wheel events for scrolling
            self.root.bind_all("<MouseWheel>",
                              lambda event: on_mousewheel(self, event, self.current_project_canvas))
            self.root.bind_all("<Button-4>",
                              lambda event: on_linux_mousewheel(self, event, self.current_project_canvas, -1))
            self.root.bind_all("<Button-5>",
                              lambda event: on_linux_mousewheel(self, event, self.current_project_canvas, 1))